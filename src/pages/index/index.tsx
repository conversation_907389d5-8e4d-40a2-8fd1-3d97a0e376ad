import "./index.less";

import React, { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { TUILogin } from '@tencentcloud/tui-core';
import { ConversationList, Chat, ChatSetting } from '@tencentcloud/chat-uikit-react';
import { UIKitProvider } from '@tencentcloud/uikit-base-component-react';
import '@tencentcloud/chat-uikit-react/dist/esm/index.css';
import { Card, Spin, message } from "antd";
import { postUserGetByToken, UserGetByTokenResponse, postOrderList, OrderListResponse } from "@/apis";
import UserInfo from "@/components/UserInfo";

const Index = () => {
  const [urlParams] = useSearchParams();
  const token = urlParams.get("token");
  const [userInfo, setUserInfo] = useState<UserGetByTokenResponse | null>(null);
  const [orderList, setOrderList] = useState<OrderListResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [imReady, setImReady] = useState(false);

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      if (!token) {
        message.error("缺少token参数");
        return;
      }

      const response = await postUserGetByToken();
      if (response.success && response.data) {
        setUserInfo(response.data);
        return response.data;
      } else {
        message.error("获取用户信息失败");
      }
    } catch (error) {
      console.error("获取用户信息失败:", error);
      message.error("获取用户信息失败");
    }
    return null;
  };

  // 获取订单列表
  const fetchOrderList = async () => {
    try {
      const response = await postOrderList({ num: 1, row: 10 });
      if (response.success && response.data) {
        setOrderList(response.data);
      }
    } catch (error) {
      console.error("获取订单列表失败:", error);
    }
  };

  // 初始化IM
  const initIM = async (userData: UserGetByTokenResponse) => {
    try {
      // 检查是否有userSig，如果没有则跳过IM初始化
      if (!userData.userSig) {
        console.warn("缺少userSig，跳过IM初始化");
        setImReady(false);
        return;
      }

      const config = {
        SDKAppID: 1400000000, // 需要替换为实际的SDKAppID
        userID: userData.userId,
        userSig: userData.userSig,
      };

      await TUILogin.login({
        ...config,
        useUploadPlugin: true
      });

      setImReady(true);
    } catch (error) {
      console.error("IM初始化失败:", error);
      // 不显示错误消息，因为可能是配置问题
      setImReady(false);
    }
  };

  useEffect(() => {
    const init = async () => {
      setLoading(true);
      const userData = await fetchUserInfo();
      if (userData) {
        await Promise.all([
          fetchOrderList(),
          initIM(userData)
        ]);
      }
      setLoading(false);
    };

    init();
  }, [token]);

  if (loading) {
    return (
      <div className="chat-page-loading">
        <Spin size="large" />
        <div>正在初始化...</div>
      </div>
    );
  }

  if (!userInfo) {
    return (
      <div className="chat-page-error">
        <div>用户信息加载失败</div>
      </div>
    );
  }

  return (
    <div className="chat-page">
      <UIKitProvider language='zh-CN' theme='light'>
        <div className="chat-page-left">
          {imReady ? (
            <>
              <ConversationList />
              <Chat />
            </>
          ) : (
            <div className="chat-disabled">
              <div className="chat-disabled-message">
                <h3>聊天功能暂不可用</h3>
                <p>请联系管理员配置IM服务</p>
              </div>
            </div>
          )}
        </div>
        <div className="chat-page-right">
          <Card title="用户信息" className="user-info-card">
            <UserInfo
              name={userInfo.nickname}
              avatar={userInfo.avatar}
              role={userInfo.roleId}
            />
            <div className="user-details">
              <div className="user-detail-item">
                <span className="label">手机号:</span>
                <span className="value">{userInfo.phone}</span>
              </div>
              <div className="user-detail-item">
                <span className="label">主营业务:</span>
                <span className="value">{userInfo.mainBus}</span>
              </div>
              <div className="user-detail-item">
                <span className="label">个性签名:</span>
                <span className="value">{userInfo.sign}</span>
              </div>
            </div>
          </Card>

          <Card title="订单信息" className="order-info-card">
            {orderList.length > 0 ? (
              <div className="order-list">
                {orderList.map((order) => (
                  <div key={order.id} className="order-item">
                    <div className="order-header">
                      <span className="order-no">订单号: {order.orderNo}</span>
                      <span className={`order-status status-${order.status}`}>
                        {order.status === 0 ? '待支付' :
                         order.status === 100 ? '已支付' :
                         order.status === 200 ? '已退款' : '其他'}
                      </span>
                    </div>
                    <div className="order-amount">金额: ¥{order.amount}</div>
                    <div className="order-time">创建时间: {order.createTime}</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-orders">暂无订单信息</div>
            )}
          </Card>
        </div>
      </UIKitProvider>
    </div>
  );
};

export default Index;
