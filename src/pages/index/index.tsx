import "./index.less";

import React from "react";
import {
  UserPostInfoResponse,
} from "@/apis";
import { SourceListItem } from "@/apis/exact";

import { useSearchParams } from "react-router-dom";

interface DetailProps extends UserPostInfoResponse {
  sourceList: SourceListItem[];
  minWeight?: number; // 最小重量
  maxWeight?: number; // 最大重量
  variety?: string; // 品种
  category?: string; // 品类
  billType?: string; // 磅单类型
  invoiceType?: string; // 开票类型
  roleId?: string; // 角色id
}

const Index = () => {
  const [urlParams] = useSearchParams();
  // const id = urlParams.get("id");
  const params = urlParams.get("token");
  return (
    <div>1233</div>
  );
};

export default Index;
