import { Method } from "@/utils/interface";
import request from "@/utils/request";

/**
 * @title 获取用户角色列表
 * @url config/app-role/list
 */

export interface ConfigAppRoleListRequest {
  // No parameters returned
}

export interface ConfigAppRoleListResponse {
  roleId: string; // 角色 id
  roleName: string; // 角色名称
  roleCode: string; // 角色标识
  roleDesc: string; // 角色描述
}

// 获取用户角色列表
export async function postConfigAppRoleList(body?: ConfigAppRoleListRequest) {
  return request<ConfigAppRoleListResponse[]>(`config/app-role/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 获取猪价行情
 * @url config/home/<USER>
 */

export interface ConfigHomePigMarketRequest {
  // No parameters returned
}

export interface ConfigHomePigMarketResponse {
  id: string; // 主键id
  type: number; // 类型 1-猪价行情
  jumpType: number; // 类型 0-无跳转 1-url
  jumpTarget: string; // 跳转目标
  content: string; // 内容，html模式
}

// 获取猪价行情
export async function postConfigHomePigMarket(
  body?: ConfigHomePigMarketRequest,
) {
  return request<ConfigHomePigMarketResponse>(`config/home/<USER>
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 获取实名认证certifyId
 * @url oauth/face/certifyId
 */

export interface OauthFaceCertifyIdRequest {
  metaInfo?: any; // **客户端sdk获取的环境参数**
}

export interface OauthFaceCertifyIdResponse {
  certifyId: string; // 实名认证certifyId
}

// 获取实名认证certifyId
export async function postOauthFaceCertifyId(body: OauthFaceCertifyIdRequest) {
  return request<OauthFaceCertifyIdResponse>(`oauth/face/certifyId`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 获取实名认证结果
 * @url oauth/face/certifyResult
 */

export interface OauthFaceCertifyResultRequest {
  certifyId?: any; // **实名认证唯一标识**
}

export interface OauthFaceCertifyResultResponse {
  certifyPass: string; // 认证结果 T:通过 F:不通过
  realName?: string; // 真实姓名
  idCard?: string; // 身份证号码
}

// 获取实名认证结果
export async function postOauthFaceCertifyResult(
  body: OauthFaceCertifyResultRequest,
) {
  return request<OauthFaceCertifyResultResponse>(`oauth/face/certifyResult`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 检查版本更新
 * @url config/version/check
 */

export interface ConfigVersionCheckRequest {
  // No parameters returned
}

export interface ConfigVersionCheckResponse {
  type: number; // 类型 0-提示更新 1-强制更新
  device: number; // 设备 1-安卓 2-ios
  appVersion: string; // 最新版本号
  description: string; // 更新内容描述
  target?: string; // 跳转地址
}

// 检查版本更新
export async function postConfigVersionCheck(body?: ConfigVersionCheckRequest) {
  return request<ConfigVersionCheckResponse>(`config/version/check`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 获取竞拍列表
 * @url auction/list
 */

export interface AuctionListRequest {
  status?: any; // **状态 0-未开始 1-进行中 100-已结束**
  type?: number; // 分类 0-未知
  num?: number; // 当前页，默认为1
  row?: number; // 每页显示数，默认为10
}

export interface AuctionListResponse {
  id: string; // 主键id
  type: number; // 分类 0-未知
  varieties: string; // 品种
  equalWeight: string; // 均重
  urls: any[]; // 图片地址列表
  sellCnt: number; // 出售数量
  lowPrice: string; // 底价
  startDate: string; // 开始时间
  endDate: string; // 结束时间
  poundType?: string; // 磅单类型
  billType?: string; // 开票类型
  pigFarm?: string; // 猪场名称
  contacts?: string; // 联系人
  contactInfo?: string; // 联系方式
  pullTime: string; // 拉猪时间
  remark?: string; // 备注
  status: number; // 状态 0-未开始 1-进行中 100-已结束
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 获取竞拍列表
export async function postAuctionList(body: AuctionListRequest) {
  return request<AuctionListResponse[]>(`auction/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 获取配置信息
 * @url config/info
 */

export interface ConfigInfoRequest {
  // No parameters returned
}

export interface ConfigInfoResponse {
  depositConfig: {
    buyPigDeposit: any; // 购买生猪押金（单价，每头保证金）
    buyPigMaxDeposit: any; // 购买生猪最大保证金
    sellPigDeposit: any; // 生猪出售帖子押金
    sellPorkDeposit: any; // 猪肉出售帖子押金
    driverDeposit: any; // 司机押金
  };
}

// 获取配置信息
export async function postConfigInfo(body?: ConfigInfoRequest) {
  return request<ConfigInfoResponse>(`config/info`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 注册登录校验
 * @url user/check
 */

export interface UserCheckRequest {
  phone?: any; // **手机号码**
}

export interface UserCheckResponse {
  operate: number; // 操作 1-注册 2-登录
}

// 注册登录校验
export async function postUserCheck(body: UserCheckRequest) {
  return request<UserCheckResponse>(`user/check`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 注册登录
 * @url user/signup
 */

export interface UserSignupRequest {
  type?: any; // **类型 0-手机号+短信 1-手机号+密码**
  phone?: any; // **手机号码**
  password?: string; // 密码，AES 加密后
  code?: string; // 验证码
  nickname?: string; // 昵称
  sex?: number; // 性别 0-未知 1-男 2-女
  birthday?: string; // 出生年月 yyyy-MM-dd
}

export interface UserSignupResponse {
  userId: string; // 用户id
  phone: string; // 手机号码
  avatar: string; // 头像地址
  nickname: string; // 昵称
  sex: number; // 性别 0-未知 1-男 2-女
  age: number; // 年龄
  birthday: string; // 出生年月
  mainBus: string; // 主营业务
  sign: string; // 个性签名
  fans: string; // 粉丝数
  follows: string; // 关注数
  roleId?: string; // 角色id
  token: string; // 授权token
  userSig: string; // 腾讯IM的userSig
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 注册登录
export async function postUserSignup(body: UserSignupRequest) {
  return request<UserSignupResponse>(`user/signup`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 小程序授权登录
 * @url user/mini/signup
 */

export interface UserMiniSignupRequest {
  code?: any; // **微信小程序授权code**
  phone?: string; // 手机号码
  nickname?: string; // 昵称
}

export interface UserMiniSignupResponse {
  userId: string; // 用户id
  phone: string; // 手机号码
  avatar: string; // 头像地址
  nickname: string; // 昵称
  sex: number; // 性别 0-未知 1-男 2-女
  age: number; // 年龄
  birthday: string; // 出生年月
  mainBus: string; // 主营业务
  sign: string; // 个性签名
  fans: string; // 粉丝数
  follows: string; // 关注数
  roleId?: string; // 角色id
  token: string; // 授权token
  openId: string; // 微信授权openId
  userSig: string; // 腾讯IM的userSig
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 小程序授权登录
export async function postUserMiniSignup(body: UserMiniSignupRequest) {
  return request<UserMiniSignupResponse>(`user/mini/signup`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 小程序openId登录
 * @url user/mini/login
 */

export interface UserMiniLoginRequest {
  openId?: any; // **用户openId**
}

export interface UserMiniLoginResponse {
  userId: string; // 用户id
  phone: string; // 手机号码
  avatar: string; // 头像地址
  nickname: string; // 昵称
  sex: number; // 性别 0-未知 1-男 2-女
  age: number; // 年龄
  birthday: string; // 出生年月
  mainBus: string; // 主营业务
  sign: string; // 个性签名
  fans: string; // 粉丝数
  follows: string; // 关注数
  roleId?: string; // 角色id
  token: string; // 授权token
  userSig: string; // 腾讯IM的userSig
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 小程序openId登录
export async function postUserMiniLogin(body: UserMiniLoginRequest) {
  return request<UserMiniLoginResponse>(`user/mini/login`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 发送验证码
 * @url user/sms/code
 */

export interface UserSmsCodeRequest {
  phone?: any; // **手机号码**
}

export interface UserSmsCodeResponse {
  // No parameters returned
}

// 发送验证码
export async function postUserSmsCode(body: UserSmsCodeRequest) {
  return request<UserSmsCodeResponse>(`user/sms/code`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 小程序绑定手机号码
 * @url user/mini/bind
 */

export interface UserMiniBindRequest {
  code?: any; // **小程序授权code**
}

export interface UserMiniBindResponse {
  // No parameters returned
}

// 小程序绑定手机号码
export async function postUserMiniBind(body: UserMiniBindRequest) {
  return request<UserMiniBindResponse>(`user/mini/bind`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 获取用户信息（根据 id）
 * @url user/info
 */

export interface UserInfoRequest {
  userId?: any; // **用户id**
}

export interface UserInfoResponse {
  userId: string; // 用户id
  phone: string; // 手机号码
  avatar: string; // 头像地址
  nickname: string; // 昵称
  name: string; // 真实姓名
  idCard: string; // 身份证号
  sex: number; // 性别 0-未知 1-男 2-女
  age: number; // 年龄
  birthday: string; // 出生年月
  mainBus: string; // 主营业务
  sign: string; // 个性签名
  fans: string; // 粉丝数
  follows: string; // 关注数
  roleId?: string; // 角色id
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 获取用户信息（根据 id）
export async function postUserInfo(body: UserInfoRequest) {
  return request<UserInfoResponse>(`user/info`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 获取用户信息（根据token）
 * @url user/getByToken
 */

export interface UserGetByTokenRequest {
  // No parameters returned
}

export interface UserGetByTokenResponse {
  userId: string; // 用户id
  phone: string; // 手机号码
  avatar: string; // 头像地址
  nickname: string; // 昵称
  name?: string; // 真实姓名
  idCard: string; // 身份证号
  sex: number; // 性别 0-未知 1-男 2-女
  age: number; // 年龄
  birthday: string; // 出生年月
  mainBus: string; // 主营业务
  sign: string; // 个性签名
  fans: string; // 粉丝数
  follows: string; // 关注数
  roleId?: string; // 角色id
  token: string; // 授权token
  userSig: string; // 腾讯IM的userSig
  certifyStatus: number; // 认证状态 1-已认证 0-未认证
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 获取用户信息（根据token）
export async function postUserGetByToken(body?: UserGetByTokenRequest) {
  return request<UserGetByTokenResponse>(`user/getByToken`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 修改用户信息
 * @url user/update
 */

export interface UserUpdateRequest {
  nickname?: string; // 昵称
  sex?: number; // 性别 0-未知 1-男 2-女
  birthday?: string; // 出生年月 yyyy-MM-dd
  mainBus?: string; // 主营业务
  sign?: string; // 个性签名
  roleId?: string; // 角色id
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

export interface UserUpdateResponse {
  // No parameters returned
}

// 修改用户信息
export async function postUserUpdate(body: UserUpdateRequest) {
  return request<UserUpdateResponse>(`user/update`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 重置用户密码
 * @url user/resetPwd
 */

export interface UserResetPwdRequest {
  phone?: any; // **手机号码**
  password?: any; // **密码，AES 加密后**
  code?: any; // **验证码**
}

export interface UserResetPwdResponse {
  // No parameters returned
}

// 重置用户密码
export async function postUserResetPwd(body: UserResetPwdRequest) {
  return request<UserResetPwdResponse>(`user/resetPwd`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 用户关注
 * @url user/follow
 */

export interface UserFollowRequest {
  userId?: any; // **用户id**
}

export interface UserFollowResponse {
  // No parameters returned
}

// 用户关注
export async function postUserFollow(body: UserFollowRequest) {
  return request<UserFollowResponse>(`user/follow`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 通讯录列表
 * @url user/contact
 */

export interface UserContactRequest {
  roleId?: string; // 角色id
  keyword?: string; // 搜索关键字（主营业务和昵称），支持模糊查询
  province?: string; // 省份，支持模糊查询
  city?: string; // 城市，支持模糊查询
  num?: number; // 当前页，默认为1
  row?: number; // 显示数，默认为10
}

export interface UserContactResponse {
  userId: string; // 用户id
  nickname: string; // 昵称
  mainBus: string; // 主营业务
  roleId?: string; // 角色id
  isFollow?: boolean; // 是否关注
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 通讯录列表
export async function postUserContact(body: UserContactRequest) {
  return request<UserContactResponse[]>(`user/contact`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 关注列表
 * @url user/follow/list
 */

export interface UserFollowListRequest {
  userId?: any; // **用户id**
}

export interface UserFollowListResponse {
  userId: string; // 用户id
  nickname: string; // 昵称
  mainBus: string; // 主营业务
  roleId?: string; // 角色id
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 关注列表
export async function postUserFollowList(body: UserFollowListRequest) {
  return request<UserFollowListResponse[]>(`user/follow/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 粉丝列表
 * @url user/fans/list
 */

export interface UserFansListRequest {
  userId?: any; // **用户id**
}

export interface UserFansListResponse {
  userId: string; // 用户id
  nickname: string; // 昵称
  mainBus: string; // 主营业务
  roleId?: string; // 角色id
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 粉丝列表
export async function postUserFansList(body: UserFansListRequest) {
  return request<UserFansListResponse[]>(`user/fans/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 意见反馈
 * @url user/feedback
 */

export interface UserFeedbackRequest {
  description?: any; // **内容描述**
  type?: number; // 类型 0-默认
  picUrls?: string; // 图片列表，多图已逗号隔开
  email?: string; // 邮箱
}

export interface UserFeedbackResponse {
  // No parameters returned
}

// 意见反馈
export async function postUserFeedback(body: UserFeedbackRequest) {
  return request<UserFeedbackResponse>(`user/feedback`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 用户举报
 * @url user/report
 */

export interface UserReportRequest {
  businessType?: any; // **业务类型 1-用户 2-帖子**
  businessId?: any; // **业务id，对应举报的主键id**
  type?: number; // 类型 0-默认
}

export interface UserReportResponse {
  // No parameters returned
}

// 用户举报
export async function postUserReport(body: UserReportRequest) {
  return request<UserReportResponse>(`user/report`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 用户注销
 * @url user/off
 */

export interface UserOffRequest {
  // No parameters returned
}

export interface UserOffResponse {
  // No parameters returned
}

// 用户注销
export async function postUserOff(body?: UserOffRequest) {
  return request<UserOffResponse>(`user/off`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 用户认证信息
 * @url user-certify/info
 */

export interface UserCertifyInfoRequest {
  userId?: any; // **用户id**
  type?: any; // **类型 0-个人认证 1-企业认证 2-司机认证 3-猪场认证**
}

export interface UserCertifyInfoResponse {
  userId: string; // 用户id
  type?: number; // 类型 0-个人认证 1-企业认证 2-司机认证 3-猪场认证
  name?: string; // 真实姓名
  idCard?: string; // 身份证号
  frondIdCard?: string; // 身份证正面
  backIdCard?: string; // 身份证背面
  handIdCard?: string; // 手持身份证
  companyName?: string; // 企业名称
  driverLicense?: string; // 驾驶证
  vehicleLicense?: string; // 行驶证
  businessLicense?: string; // 业务证件(营业执照/备案证明)
  status?: number; // 审核状态 0-待审核 -1-审核未通过 100-审核已通过
  reason?: string; // 审核结果
}

// 用户认证信息
export async function postUserCertifyInfo(body: UserCertifyInfoRequest) {
  return request<UserCertifyInfoResponse>(`user-certify/info`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 提交认证
 * @url user-certify/verify
 */

export interface UserCertifyVerifyRequest {
  type?: any; // **类型 1-企业认证 2-司机认证**
  frondIdCard?: string; // 身份证正面
  backIdCard?: string; // 身份证背面
  handIdCard?: string; // 手持身份证
  handBackIdCard?: string; // 手持背面身份证
  name?: string; // 名称(企业名称、猪场名称等)
  driverLicense?: string; // 驾驶证
  vehicleLicense?: string; // 行驶证
  businessLicense?: string; // 业务证件(营业执照, 备案证件)
}

export interface UserCertifyVerifyResponse {
  // No parameters returned
}

// 提交认证
export async function postUserCertifyVerify(body: UserCertifyVerifyRequest) {
  return request<UserCertifyVerifyResponse>(`user-certify/verify`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 用户认证列表
 * @url user-certify/list
 */

export interface UserCertifyListRequest {
  userId?: any; // **用户id**
}

export interface UserCertifyListResponse {
  type?: number; // 类型 1-企业认证 2-司机认证 3-猪场认证
  name: string; // 认证名称
  status?: number; // 认证状态 0-待审核 -1-审核失败 100-审核成功
}

// 用户认证列表
export async function postUserCertifyList(body: UserCertifyListRequest) {
  return request<UserCertifyListResponse[]>(`user-certify/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 添加、修改用户卡片
 * @url user-card/addOrUpdate
 */

export interface UserCardAddOrUpdateRequest {
  type?: any; // **类型 1-银行卡**
  name?: string; // 姓名
  cardNo?: string; // 卡号
  address?: string; // 地址
  phone?: string; // 手机号码
}

export interface UserCardAddOrUpdateResponse {
  // No parameters returned
}

// 添加、修改用户卡片
export async function postUserCardAddOrUpdate(
  body: UserCardAddOrUpdateRequest,
) {
  return request<UserCardAddOrUpdateResponse>(`user-card/addOrUpdate`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 获取用户卡片列表
 * @url user-card/list
 */

export interface UserCardListRequest {
  // No parameters returned
}

export interface UserCardListResponse {
  id?: string; // 主键id
  type?: number; // 类型 1-银行卡
  name?: string; // 姓名
  cardNo?: string; // 卡号
  address?: string; // 地址
  phone?: string; // 手机号码
  isDefault?: number; // 是否默认 0-否 1-是
  lastUpdateTime?: string; // 最后更新时间
}

// 获取用户卡片列表
export async function postUserCardList(body?: UserCardListRequest) {
  return request<UserCardListResponse[]>(`user-card/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 删除用户卡片
 * @url user-card/delete
 */

export interface UserCardDeleteRequest {
  id?: any; // **用户卡片主键id**
}

export interface UserCardDeleteResponse {
  // No parameters returned
}

// 删除用户卡片
export async function postUserCardDelete(body: UserCardDeleteRequest) {
  return request<UserCardDeleteResponse>(`user-card/delete`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 用户交易记录列表
 * @url user-trade-detail/list
 */

export interface UserTradeDetailListRequest {
  startTime?: string; // 开始时间 yyyy-MM-dd
  endTime?: string; // 结束时间 yyyy-MM-dd
  type?: number; // 类型 1001-押金
  lastId?: string; // 最后评论id
  row?: number; // 显示数，默认为10
}

export interface UserTradeDetailListResponse {
  id: string; // 主键id
  userId: string; // 用户id
  title: string; // 标题
  tradeNo: string; // 交易号
  type: number; // 类型
  dateline: string; // 时间戳
}

// 用户交易记录列表
export async function postUserTradeDetailList(
  body: UserTradeDetailListRequest,
) {
  return request<UserTradeDetailListResponse[]>(`user-trade-detail/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 发布、修改帖子
 * @url user-post/addOrUpdate
 */

export interface UserPostAddOrUpdateRequest {
  userId?: any; // **用户id**
  type?: any; // **类型 0-未知 100-生猪求购 200-生猪出售 300-叫车需求 400-行情分析 500-交流分享 600-好物推荐 700-行业动态 800-求职招聘 900-猪场业务 1000-猪肉批发 1100-猪肉求购**
  content?: any; // **内容**
  postId?: string; // 帖子 id，发布新帖子，不需要传该字段
  subType?: string; // 子类型
  porkType?: string; // 猪肉类型
  porkParts?: string; // 猪肉部位
  pigType?: string; // 生猪类型
  price?: any; // 单价/均单价
  weight?: any; // 重量/均重量
  minWeight?: any; // 重量区间：最小重量
  maxWeight?: any; // 重量区间：最大重量
  variety?: string; // 品种
  category?: string; // 品类
  billType?: string; // 磅单类型
  invoiceType?: string; // 开票类型
  growthDay?: number; // 生长天数
  deconAddr?: string; // 洗消地址
  deconCost?: any; // 洗消费用
  road?: any; // 道路（用于提示能够用过多少米的车辆）
  vehicleLen?: any; // 车辆长度
  number?: any; // 数量
  contacts?: string; // 联系人
  mobile?: string; // 手机号
  imgUrls?: string; // 图片地址（多图片以逗号隔开）
  videoUrls?: string; // 视频地址（多视频以逗号隔开）
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
  addressDest: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

export interface UserPostAddOrUpdateResponse {
  id: string; // 帖子 id
  userId: string; // 用户id
  nickname: string; // 昵称
  type: number; // 类型
  subType?: number; // 子类型
  avatar: string; // 头像
  title: string; // 标题
  content: string; // 内容
  porkType?: string; // 猪肉类型
  porkParts?: string; // 猪肉部位
  pigType?: string; // 生猪类型
  price?: any; // 单价
  weight?: any; // 重量
  number?: any; // 数量
  praises: string; // 点赞数
  comments: string; // 评论数
  collects: string; // 收藏数
  contacts?: string; // 联系人
  mobile?: string; // 联系方式
  dateline: number; // 时间戳
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
  addressDest: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 发布、修改帖子
export async function postUserPostAddOrUpdate(
  body: UserPostAddOrUpdateRequest,
) {
  return request<UserPostAddOrUpdateResponse>(`user-post/addOrUpdate`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 帖子列表
 * @url user-post/list
 */

export interface UserPostListRequest {
  userId?: string; // 用户id
  type?: number; // 帖子类型 0-未知 100-生猪求购 200-生猪出售 300-叫车需求 400-行情分析 500-交流分享 600-好物推荐 700-行业动态 800-求职招聘 900-猪场业务 1000-猪肉批发 1100-猪肉求购
  subType?: string; // 帖子子类型
  province?: string; // 省份，支持模糊查询
  city?: string; // 城市，支持模糊查询
  keyword?: string; // 搜索关键词，支持模糊查询
  lastId?: string; // 最后评论id
  row?: number; // 显示数，默认为10
}

export interface UserPostListResponse {
  id: string; // 帖子 id
  userId: string; // 用户id
  nickname: string; // 昵称
  type: number; // 类型
  subType?: string; // 子类型
  avatar: string; // 头像
  title: string; // 标题
  content: string; // 内容
  porkType?: string; // 猪肉类型
  porkParts?: string; // 猪肉部位
  pigType?: string; // 生猪类型
  price?: any; // 单价
  weight?: any; // 重量
  minWeight?: any; // 重量区间：最小重量
  maxWeight?: any; // 重量区间：最大重量
  variety?: string; // 品种
  category?: string; // 品类
  billType?: string; // 磅单类型
  invoiceType?: string; // 开票类型
  growthDay?: number; // 生长天数
  deconAddr?: string; // 洗消地址
  deconCost?: any; // 洗消费用
  road?: any; // 道路（用于提示能够用过多少米的车辆）
  vehicleLen?: any; // 车辆长度
  number?: any; // 数量
  praises: string; // 点赞数
  comments: string; // 评论数
  collects: string; // 收藏数
  contacts?: string; // 联系人
  mobile?: string; // 联系方式
  isPraise?: boolean; // 是否点赞
  isCollect?: boolean; // 是否收藏
  isFollow?: boolean; // 是否关注
  status: number; // 状态 0-正常 -1-下架
  dateline: string; // 时间戳
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 帖子列表
export async function postUserPostList(body: UserPostListRequest) {
  return request<UserPostListResponse[]>(`user-post/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 帖子详情
 * @url user-post/info
 */

export interface UserPostInfoRequest {
  postId?: any; // **帖子id**
}

export interface UserPostInfoResponse {
  id: string; // 帖子 id
  userId: string; // 用户id
  nickname: string; // 昵称
  type: number; // 类型
  subType?: string; // 子类型
  avatar: string; // 头像
  title: string; // 标题
  content: string; // 内容
  porkType?: string; // 猪肉类型
  porkParts?: string; // 猪肉部位
  pigType?: string; // 生猪类型
  price?: any; // 单价
  weight?: any; // 重量
  minWeight?: any; // 重量区间：最小重量
  maxWeight?: any; // 重量区间：最大重量
  variety?: string; // 品种
  category?: string; // 品类
  billType?: string; // 磅单类型
  invoiceType?: string; // 开票类型
  growthDay?: number; // 生长天数
  deconAddr?: string; // 洗消地址
  deconCost?: any; // 洗消费用
  road?: any; // 道路（用于提示能够用过多少米的车辆）
  vehicleLen?: any; // 车辆长度
  number?: any; // 数量
  praises: string; // 点赞数
  comments: string; // 评论数
  collects: string; // 收藏数
  isPraise?: boolean; // 是否点赞
  isCollect?: boolean; // 是否收藏
  mobile?: string; // 联系方式
  status?: any; // 状态 0-正常 1-待支付押金 2-交易进行中 100-已出售 -1-下架 -2-待审核
  dateline: string; // 时间戳
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
  addressDest: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 帖子详情
export async function postUserPostInfo(body: UserPostInfoRequest) {
  return request<UserPostInfoResponse>(`user-post/info`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 评论帖子
 * @url user-post/comment
 */

export interface UserPostCommentRequest {
  postId?: any; // **帖子id**
  content?: any; // **内容**
  replyUserId?: string; // 回复用户id
  commentId?: string; // 回复评论id
}

export interface UserPostCommentResponse {
  id: string; // 评论id
  postId: string; // 帖子id
  userId: string; // 用户id
  nickname: string; // 昵称
  avatar: string; // 头像
  replyUserId?: string; // 回复用户id
  replyUserName?: string; // 回复用户昵称
  content: string; // 内容
  praises: string; // 点赞数
  dateline: string; // 时间 yyyy-MM-dd HH:mm:ss
}

// 评论帖子
export async function postUserPostComment(body: UserPostCommentRequest) {
  return request<UserPostCommentResponse>(`user-post/comment`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 点赞的帖子列表
 * @url user-post/praise/list
 */

export interface UserPostPraiseListRequest {
  userId?: any; // **用户id**
  lastId?: string; // 最后评论id
  row?: number; // 显示数，默认为10
}

export interface UserPostPraiseListResponse {
  id: string; // 帖子 id
  userId: string; // 用户id
  nickname: string; // 昵称
  type: number; // 类型
  subType?: string; // 子类型
  avatar: string; // 头像
  title: string; // 标题
  content: string; // 内容
  praises: string; // 点赞数
  comments: string; // 评论数
  collects: string; // 收藏数
  contacts?: string; // 联系人
  mobile?: string; // 联系方式
  isPraise?: boolean; // 是否点赞
  isCollect?: boolean; // 是否收藏
  dateline: string; // 时间戳
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 点赞的帖子列表
export async function postUserPostPraiseList(body: UserPostPraiseListRequest) {
  return request<UserPostPraiseListResponse[]>(`user-post/praise/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 收藏的帖子列表
 * @url user-post/collect/list
 */

export interface UserPostCollectListRequest {
  userId?: any; // **用户id**
  lastId?: string; // 最后评论id
  row?: number; // 显示数，默认为10
}

export interface UserPostCollectListResponse {
  id: string; // 帖子 id
  userId: string; // 用户id
  nickname: string; // 昵称
  type: number; // 类型
  subType?: string; // 子类型
  avatar: string; // 头像
  title: string; // 标题
  content: string; // 内容
  praises: string; // 点赞数
  comments: string; // 评论数
  collects: string; // 收藏数
  contacts?: string; // 联系人
  mobile?: string; // 联系方式
  isPraise?: boolean; // 是否点赞
  isCollect?: boolean; // 是否收藏
  dateline: string; // 时间戳
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 收藏的帖子列表
export async function postUserPostCollectList(
  body: UserPostCollectListRequest,
) {
  return request<UserPostCollectListResponse[]>(`user-post/collect/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 删除帖子
 * @url user-post/delete/{id}
 */

export interface UserPostDeleteRequest {
  id?: any; // **帖子id，携带在url上**
}

export interface UserPostDeleteResponse {
  // No parameters returned
}

// 删除帖子
export async function postUserPostDelete(body: UserPostDeleteRequest) {
  return request<UserPostDeleteResponse>(`user-post/delete/${body?.id}`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 点赞帖子
 * @url user-post/praise/{id}
 */

export interface UserPostPraiseRequest {
  id?: any; // **帖子id，携带在url上**
}

export interface UserPostPraiseResponse {
  praises: string; // 点赞数
  isPraise: boolean; // 是否点赞
}

// 点赞帖子
export async function postUserPostPraise(body: UserPostPraiseRequest) {
  return request<UserPostPraiseResponse>(`user-post/praise/${body?.id}`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 收藏帖子
 * @url user-post/collect/{id}
 */

export interface UserPostCollectRequest {
  id?: any; // **帖子id，携带在url上**
}

export interface UserPostCollectResponse {
  collects: string; // 收藏数
  isCollect: boolean; // 是否收藏
}

// 收藏帖子
export async function postUserPostCollect(body: UserPostCollectRequest) {
  return request<UserPostCollectResponse>(`user-post/collect/${body?.id}`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 点赞帖子评论
 * @url user-post/comment/praise/{id}
 */

export interface UserPostCommentPraiseRequest {
  id?: any; // **评论id，携带在url上**
}

export interface UserPostCommentPraiseResponse {
  praises: string; // 点赞数
  isPraise: boolean; // 是否点赞
}

// 点赞帖子评论
export async function postUserPostCommentPraise(
  body: UserPostCommentPraiseRequest,
) {
  return request<UserPostCommentPraiseResponse>(
    `user-post/comment/praise/${body?.id}`,
    {
      method: Method.POST,

      data: body,
    },
  );
}

/**
 * @title 删除帖子评论
 * @url user-post/comment/delete/{id}
 */

export interface UserPostCommentDeleteRequest {
  id?: any; // **评论id，携带在url上**
}

export interface UserPostCommentDeleteResponse {
  // No parameters returned
}

// 删除帖子评论
export async function postUserPostCommentDelete(
  body: UserPostCommentDeleteRequest,
) {
  return request<UserPostCommentDeleteResponse>(
    `user-post/comment/delete/${body?.id}`,
    {
      method: Method.POST,

      data: body,
    },
  );
}

/**
 * @title 帖子评论列表
 * @url user-post/comment/list
 */

export interface UserPostCommentListRequest {
  postId?: any; // **帖子id**
  commentId?: string; // 评论id
  lastId?: string; // 最后评论id
  row?: number; // 显示数，默认为10
}

export interface UserPostCommentListResponse {
  id: string; // 评论id
  postId: string; // 帖子id
  userId: string; // 用户id
  nickname: string; // 昵称
  avatar: string; // 头像
  replyUserId?: string; // 回复用户id
  replyUserName?: string; // 回复用户昵称
  content: string; // 内容
  praises: string; // 点赞数
  replyCnt?: string; // 回复数
  isPraise?: boolean; // 是否点赞
  dateline: string; // 时间戳
  replyList?: any[]; // 二级评论列表，内容跟上级一致
}

// 帖子评论列表
export async function postUserPostCommentList(
  body: UserPostCommentListRequest,
) {
  return request<UserPostCommentListResponse[]>(`user-post/comment/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 短视频列表
 * @url user-post/video/list
 */

export interface UserPostVideoListRequest {
  userId?: string; // 用户id
  lastSource?: string; // 最后分值，分页专用
  row?: number; // 显示数，默认为10
}

export interface UserPostVideoListResponse {
  id: string; // 帖子 id
  userId: string; // 用户id
  nickname: string; // 昵称
  type: number; // 类型
  subType?: string; // 子类型
  avatar: string; // 头像
  content: string; // 内容
  praises: string; // 点赞数
  comments: string; // 评论数
  collects: string; // 收藏数
  contacts?: string; // 联系人
  mobile?: string; // 联系方式
  isPraise?: boolean; // 是否点赞
  isCollect?: boolean; // 是否收藏
  isFollow?: boolean; // 是否关注
  dateline: string; // 时间戳
  url: string; // 视频地址
  source: string; // 视频分值
  addressInfo: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
  addressDest: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 短视频列表
export async function postUserPostVideoList(body: UserPostVideoListRequest) {
  return request<UserPostVideoListResponse[]>(`user-post/video/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 更改帖子状态
 * @url user-post/updateStatus
 */

export interface UserPostUpdateStatusRequest {
  postId?: any; // **帖子id**
  status?: any; // **状态 0-正常/上架**
}

export interface UserPostUpdateStatusResponse {
  // No parameters returned
}

// 更改帖子状态
export async function postUserPostUpdateStatus(
  body: UserPostUpdateStatusRequest,
) {
  return request<UserPostUpdateStatusResponse>(`user-post/updateStatus`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 下架帖子
 * @url user-post/takeOff/{id}
 */

export interface UserPostTakeOffRequest {
  id?: any; // **帖子id，携带在url上**
}

export interface UserPostTakeOffResponse {
  // No parameters returned
}

// 下架帖子
export async function postUserPostTakeOff(body: UserPostTakeOffRequest) {
  return request<UserPostTakeOffResponse>(`user-post/takeOff/${body?.id}`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 发送IM消息
 * @url message/send
 */

export interface MessageSendRequest {
  fromUser?: any; // **发送者**
  toUser?: any; // **接收者**
  msgType?: any; // **消息类型 1000-系统消息 2000-私聊消息**
  data?: any; // **消息内容体**
  time?: number; // 时间戳, 毫秒级
}

export interface MessageSendResponse {
  // No parameters returned
}

// 发送IM消息
export async function postMessageSend(body: MessageSendRequest) {
  return request<MessageSendResponse>(`message/send`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 创建交易订单
 * @url trade/create
 */

export interface TradeCreateRequest {
  type?: any; // **类型 1-猪肉交易 2-生猪交易**
  businessId?: any; // **业务id，type=1/2时，传帖子id**
  payType?: any; // **支付方式，UNION_APP-云闪付  WX_APP-微信APP支付**
  receiptAmount?: any; // **实付金额**
  number?: any; // 数量/重量
  receiveName?: string; // 收货人姓名
  receivePhone?: string; // 收货联系方式
  receiveAddress: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

export interface TradeCreateResponse {
  tradeNo: string; // 交易流水号
  orderNo: string; // 支付订单号
  expireTime: string; // 过期时间
}

// 创建交易订单
export async function postTradeCreate(body: TradeCreateRequest) {
  return request<TradeCreateResponse>(`trade/create`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 交易订单列表
 * @url trade/list
 */

export interface TradeListRequest {
  type?: any; // **类型 1-猪肉交易 2-生猪交易**
  userType?: any; // **用户类型 0-买家 1-卖家**
  status?: number; // 订单状态
  lastId?: string; // 最后订单id
  postId?: string; // 帖子 id
  row?: number; // 每页显示数, 默认10
}

export interface TradeListResponse {
  id: string; // 交易订单id
  tradeNo: string; // 交易流水号
  type: number; // 类型 1-猪肉交易 2-生猪交易
  businessId: string; // 业务id
  totalAmount: any; // 总金额
  receiptAmount: any; // 实付金额
  expireTime: string; // 失效时间，时间戳
  payTime?: string; // 支付时间，时间戳
  pickCode?: string; // 取货码
  pickTime?: string; // 取货时间，时间戳
  refundTime?: string; // 退款时间，时间戳
  cancelTime?: string; // 取消时间，时间戳
  number: any; // 数量
  hasTaskPhoto: boolean; // 是否有取货记录
  status: number; // 交易状态：-100-交易取消 0-买家待质押 1-待取货 2-待验货 3-卖家待质押 100-已完成 101-退款中 200-已退款
  userPost?: any; // 帖子快照（详见帖子详情接口，输出结构一致）
  receiveName?: string; // 收货人姓名
  receivePhone?: string; // 收货联系方式
  buyerUser: {
    userId?: string; // 用户id
    nickname?: string; // 昵称
    avatar?: string; // 头像
    roleId?: string; // 角色id
    orderNo?: string; // 待支付的订单号
    payType?: string; // 支付方式
    deposit?: any; // 支付押金
    payTime?: string; // 支付时间，时间戳
    refundNo?: string; // 退款流水号
    refundTime?: string; // 退款时间，时间戳
    refundAmount?: any; // 退款金额
  };
  sellerUser: {
    userId?: string; // 用户id
    nickname?: string; // 昵称
    avatar?: string; // 头像
    roleId?: string; // 角色id
    orderNo?: string; // 待支付的订单号
    payType?: string; // 支付方式
    deposit?: any; // 支付押金
    payTime?: string; // 支付时间，时间戳
    refundNo?: string; // 退款流水号
    refundTime?: string; // 退款时间，时间戳
    refundAmount?: any; // 退款金额
  };
  receiveAddress: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 交易订单列表
export async function postTradeList(body: TradeListRequest) {
  return request<TradeListResponse[]>(`trade/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 交易订单详情
 * @url trade/info/{id}
 */

export interface TradeInfoRequest {
  id?: any; // **交易订单id，携带在url上**
}

export interface TradeInfoResponse {
  id: string; // 交易订单id
  tradeNo: string; // 交易流水号
  type: number; // 类型 1-猪肉交易 2-生猪交易
  businessId: string; // 业务id
  totalAmount: any; // 总金额
  receiptAmount: any; // 实付金额
  expireTime: string; // 失效时间，时间戳
  payTime?: string; // 支付时间，时间戳
  pickCode?: string; // 取货码
  pickTime?: string; // 取货时间，时间戳
  refundTime?: string; // 退款时间，时间戳
  cancelTime?: string; // 取消时间，时间戳
  number: any; // 数量
  hasTaskPhoto: boolean; // 是否有取货记录
  status: number; // 订单状态：-100-交易取消 0-买家待质押 1-待取货 2-待验货 3-卖家待质押 100-已完成 101-退款中 200-已退款
  userPost?: any; // 帖子快照（详见帖子详情接口，输出结构一致）
  receiveName?: string; // 收货人姓名
  receivePhone?: string; // 收货联系方式
  buyerUser: {
    userId?: string; // 用户id
    nickname?: string; // 昵称
    avatar?: string; // 头像
    roleId?: string; // 角色id
    orderNo?: string; // 待支付的订单号
    payType?: string; // 支付方式
    deposit?: any; // 支付押金
    payTime?: string; // 支付时间，时间戳
    refundNo?: string; // 退款流水号
    refundTime?: string; // 退款时间，时间戳
    refundAmount?: any; // 退款金额
  };
  sellerUser: {
    userId?: string; // 用户id
    nickname?: string; // 昵称
    avatar?: string; // 头像
    roleId?: string; // 角色id
    orderNo?: string; // 待支付的订单号
    payType?: string; // 支付方式
    deposit?: any; // 支付押金
    payTime?: string; // 支付时间，时间戳
    refundNo?: string; // 退款流水号
    refundTime?: string; // 退款时间，时间戳
    refundAmount?: any; // 退款金额
  };
  receiveAddress: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

// 交易订单详情
export async function postTradeInfo(body: TradeInfoRequest) {
  return request<TradeInfoResponse>(`trade/info/${body?.id}`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 出示取货码
 * @url trade/pickCode/show
 */

export interface TradePickCodeShowRequest {
  tradeNo?: any; // **交易流水号**
}

export interface TradePickCodeShowResponse {
  tradeNo: string; // 交易流水号
  pickCode?: string; // 取货码
  pickTime?: string; // 取货时间，时间戳
}

// 出示取货码
export async function postTradePickCodeShow(body: TradePickCodeShowRequest) {
  return request<TradePickCodeShowResponse>(`trade/pickCode/show`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 校验取货码
 * @url trade/pickCode/valid
 */

export interface TradePickCodeValidRequest {
  tradeNo?: any; // **交易订单号**
  pickCode?: any; // **取货码**
}

export interface TradePickCodeValidResponse {
  tradeNo: string; // 交易流水号
  status: string; // 订单状态
}

// 校验取货码
export async function postTradePickCodeValid(body: TradePickCodeValidRequest) {
  return request<TradePickCodeValidResponse>(`trade/pickCode/valid`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 拍摄照片/视频
 * @url trade/taskPhoto
 */

export interface TradeTaskPhotoRequest {
  tradeNo?: any; // **交易订单号**
  optType?: any; // **操作类型 1-验货 2-收货**
  type?: any; // **类型 1-图片 2-视频**
  urls?: any; // **照片/视频地址,多个按逗号隔开**
  remark?: string; // 验货备注
}

export interface TradeTaskPhotoResponse {
  // No parameters returned
}

// 拍摄照片/视频
export async function postTradeTaskPhoto(body: TradeTaskPhotoRequest) {
  return request<TradeTaskPhotoResponse>(`trade/taskPhoto`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 确认收货
 * @url trade/receive
 */

export interface TradeReceiveRequest {
  tradeNo?: any; // **交易订单号**
}

export interface TradeReceiveResponse {
  tradeNo: string; // 交易流水号
  status: string; // 订单状态
}

// 确认收货
export async function postTradeReceive(body: TradeReceiveRequest) {
  return request<TradeReceiveResponse>(`trade/receive`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 交易明细列表（取货记录）
 * @url trade/detail/list
 */

export interface TradeDetailListRequest {
  tradeId?: any; // **交易订单id**
  lastId?: string; // 最后订单id
  row?: number; // 每页显示数, 默认10
}

export interface TradeDetailListResponse {
  id: string; // 主键id
  userId: string; // 用户id
  phone: string; // 手机号码
  nickname: string; // 昵称
  avatar: string; // 头像
  mainBus: string; // 主营业务
  roleId: number; // 角色id
  certifyStatus: number; // 认证状态
  picUrls: any[]; // 地址列表
  remark: string; // 备注
  dateline?: string; // 时间，时间戳
}

// 交易明细列表（取货记录）
export async function postTradeDetailList(body: TradeDetailListRequest) {
  return request<TradeDetailListResponse[]>(`trade/detail/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 申请退款
 * @url trade/refund
 */

export interface TradeRefundRequest {
  tradeNo?: any; // **交易流水号**
  refundAmount?: any; // **退款金额**
  refundReason?: any; // **退款原因**
  phone?: any; // **联系电话**
  content?: any; // **内容**
  picUrls?: string; // 图片地址
}

export interface TradeRefundResponse {
  tradeNo: string; // 交易流水号
  status: number; // 订单状态 -100-交易取消 0-待支付 1-待取货 2-待验货 3-待司机接单 4-司机已接单 5-退款中 100-交易成功 200-退款成功
}

// 申请退款
export async function postTradeRefund(body: TradeRefundRequest) {
  return request<TradeRefundResponse>(`trade/refund`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 退款详情
 * @url trade/refund/info
 */

export interface TradeRefundInfoRequest {
  tradeNo?: any; // **交易流水号**
}

export interface TradeRefundInfoResponse {
  tradeNo: string; // 商户号
  refundNo: string; // 退款流水号
  userId: string; // 用户id
  businessType: string; // 业务类型
  businessId: string; // 业务id
  payTime: string; // 付款时间
  expireTime: string; // 过期时间
  refundTime: string; // 退款申请时间
  refundAmount: string; // 退款金额
  refundReason: string; // 退款原因
  content: string; // 退款内容
  phone: string; // 退款人联系方式
  picUrls: any[]; // 图片地址列表
  rejectContent?: string; // 退款拒绝理由
  refundStatus: string; // 退款状态 0-等待卖家处理 1-退款驳回 2-平台介入 100-退款成功 101-退款撤销
  userPost: string; // 帖子快照
  buyerUser: {
    userId?: string; // 用户id
    nickname?: string; // 昵称
    avatar?: string; // 头像
    roleId?: string; // 角色id
    orderNo?: string; // 退款的支付订单号
    deposit?: any; // 支付押金
  };
  sellerUser: {
    userId?: string; // 用户id
    nickname?: string; // 昵称
    avatar?: string; // 头像
    roleId?: string; // 角色id
    deposit?: any; // 支付押金
  };
}

// 退款详情
export async function postTradeRefundInfo(body: TradeRefundInfoRequest) {
  return request<TradeRefundInfoResponse>(`trade/refund/info`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 退款详情 - 协商记录
 * @url trade/refund/detail/list
 */

export interface TradeRefundDetailListRequest {
  tradeNo?: any; // **交易流水号**
}

export interface TradeRefundDetailListResponse {
  id: string; // 主键id
  orderId: string; // 订单id
  userId: string; // 用户id
  phone: string; // 手机号码
  nickname: string; // 昵称
  avatar: string; // 用户头像
  roleId: number; // 角色id
  certifyStatus: number; // 认证状态
  picUrls: any[]; // 图片列表
  content: string; // 内容
  type: string; // 类型 0-等待卖家处理 1-退款驳回 2-平台介入 100-退款成功 101-退款撤销
  dateline: string; // 时间戳
}

// 退款详情 - 协商记录
export async function postTradeRefundDetailList(
  body: TradeRefundDetailListRequest,
) {
  return request<TradeRefundDetailListResponse[]>(`trade/refund/detail/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 退款详情 - 退款相关操作
 * @url trade/refund/detail
 */

export interface TradeRefundDetailRequest {
  tradeNo?: any; // **交易订单号**
  orderNo?: any; // **支付订单号**
  refundType?: any; // **操作类型 1-退款驳回 2-申请平台介入 100-同意退款 101-退款撤销**
  content?: string; // 内容
  picUrls?: string; // 图片urls，多个以逗号分割
}

export interface TradeRefundDetailResponse {
  // No parameters returned
}

// 退款详情 - 退款相关操作
export async function postTradeRefundDetail(body: TradeRefundDetailRequest) {
  return request<TradeRefundDetailResponse>(`trade/refund/detail`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 取消交易订单
 * @url trade/cancel
 */

export interface TradeCancelRequest {
  tradeNo?: any; // **交易流水号**
}

export interface TradeCancelResponse {
  tradeNo: string; // 商户号
  status: number; // 订单状态 -100-交易取消 0-待付款 1-待取货 2-退款中 100-交易成功 200-退款成功
  cancelTime: string; // 取消时间，时间戳
}

// 取消交易订单
export async function postTradeCancel(body: TradeCancelRequest) {
  return request<TradeCancelResponse>(`trade/cancel`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 删除交易订单
 * @url trade/delete/{id}
 */

export interface TradeDeleteRequest {
  id?: any; // **订单id，携带在url上**
}

export interface TradeDeleteResponse {
  // No parameters returned
}

// 删除交易订单
export async function postTradeDelete(body: TradeDeleteRequest) {
  return request<TradeDeleteResponse>(`trade/delete/${body?.id}`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 未完成订单数列表
 * @url trade/notEnd/list
 */

export interface TradeNotEndListRequest {
  type?: number; // 类型 1-猪肉交易 2-生猪交易
  userType?: number; // 用户类型 0-买家 1-卖家
}

export interface TradeNotEndListResponse {
  type: number; // 类型 1-猪肉交易 2-生猪交易
  num: string; // 未完成订单数
}

// 未完成订单数列表
export async function postTradeNotEndList(body: TradeNotEndListRequest) {
  return request<TradeNotEndListResponse[]>(`trade/notEnd/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 创建支付订单
 * @url order/pay/create
 */

export interface OrderPayCreateRequest {
  type?: any; // **类型 1001-帖子押金**
  payType?: any; // **支付方式，UNION_APP-云闪付 WX_APP-微信APP支付**
  amount?: any; // **支付金额**
  postId?: string; // 帖子id，`1001-帖子押金时必传`
  tradeId?: string; // 交易id，`如果有交易订单，转对应的交易id`
  number?: any; // 数量/重量
  receiveName?: string; // 收货人姓名
  receivePhone?: string; // 收货联系方式
  receiveAddress?: {
    province?: string; // 省份
    city?: number; // 城市
    district?: string; // 区县
    street?: string; // 街道、镇
    community?: string; // 社区、村
    address?: string; // 详细地址
    lng?: string; // 经度
    lat?: string; // 纬度
  };
}

export interface OrderPayCreateResponse {
  orderNo: string; // 订单号
  expireTime: string; // 过期时间
}

// 创建支付订单
export async function postOrderPayCreate(body: OrderPayCreateRequest) {
  return request<OrderPayCreateResponse>(`order/pay/create`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 生成支付参数
 * @url order/pay/params
 */

export interface OrderPayParamsRequest {
  orderNo?: any; // **订单号**
  payType?: any; // **支付方式，UNION_APP-云闪付 WX_APP-微信APP支付**
}

export interface OrderPayParamsResponse {
  appid: string; // 微信appid
  partnerid: string; // 微信mchid，商户号
  prepayid: string; // 微信预支付交易标识
  package: string; // 支付方式
  noncestr: string; // 微信返回的随机字符串
  timestamp: string; // 时间戳
  sign: string; // 签名
}

// 生成支付参数
export async function postOrderPayParams(body: OrderPayParamsRequest) {
  return request<OrderPayParamsResponse>(`order/pay/params`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 支付订单列表
 * @url order/list
 */

export interface OrderListRequest {
  userId?: any; // **用户id**
  status?: number; // 订单状态：-100-支付失败 0-待支付 100-已支付 200-已退款
  lastId?: string; // 最后订单id
  row?: number; // 每页显示数, 默认10
}

export interface OrderListResponse {
  id: string; // 订单id
  orderNo: string; // 订单号
  tradeNo: string; // 商户号
  userId: string; // 用户id
  nickname: string; // 昵称
  roleId: number; // 角色id
  avatar: string; // 头像
  postId?: string; // 帖子id
  amount: any; // 支付金额
  payType: string; // 支付方式
  expireTime: string; // 订单失效时间，时间戳
  payTime?: string; // 支付时间，时间戳
  createTime?: string; // 创建时间，时间戳
  completeTime?: string; // 完成时间，时间戳
  status: number; // 订单状态：-100-支付失败 0-待支付 100-已支付 200-已退款
}

// 支付订单列表
export async function postOrderList(body: OrderListRequest) {
  return request<OrderListResponse[]>(`order/list`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 支付订单详情
 * @url order/info/{id}
 */

export interface OrderInfoRequest {
  id?: any; // **订单id，携带在url上**
}

export interface OrderInfoResponse {
  id: string; // 订单id
  orderNo: string; // 订单号
  tradeNo: string; // 商户号
  userId: string; // 用户id
  nickname: string; // 昵称
  roleId: number; // 角色id
  avatar: string; // 头像
  postId?: string; // 帖子id
  amount: any; // 支付金额
  payType: string; // 支付方式
  expireTime: string; // 订单失效时间，时间戳
  payTime?: string; // 支付时间，时间戳
  createTime?: string; // 创建时间，时间戳
  completeTime?: string; // 完成时间，时间戳
  status: number; // 订单状态：-101-取消 -100-支付失败 0-待支付 100-已支付 200-已退款
}

// 支付订单详情
export async function postOrderInfo(body: OrderInfoRequest) {
  return request<OrderInfoResponse>(`order/info/${body?.id}`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 完成支付订单
 * @url order/pay/complete
 */

export interface OrderPayCompleteRequest {
  orderNo?: any; // **订单号**
}

export interface OrderPayCompleteResponse {
  orderNo: string; // 订单号
  status: number; // 订单状态 -100-支付失败 0-待支付 100-已支付 200-已退款
  payTime: string; // 支付时间，时间戳
}

// 完成支付订单
export async function postOrderPayComplete(body: OrderPayCompleteRequest) {
  return request<OrderPayCompleteResponse>(`order/pay/complete`, {
    method: Method.POST,

    data: body,
  });
}

/**
 * @title 删除订单
 * @url order/delete/{id}
 */

export interface OrderDeleteRequest {
  id?: any; // **订单id，携带在url上**
}

export interface OrderDeleteResponse {
  // No parameters returned
}

// 删除订单
export async function postOrderDelete(body: OrderDeleteRequest) {
  return request<OrderDeleteResponse>(`order/delete/${body?.id}`, {
    method: Method.POST,

    data: body,
  });
}
