{"name": "pig-app-h5", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "build": "vite build --mode prod", "build:dev": "vite build --mode uat", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"ahooks": "^3.8.4", "antd": "^5.26.3", "axios": "^1.8.1", "crypto-js": "^4.2.0", "eruda": "^3.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.1.5"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/crypto-js": "^4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "@vitejs/plugin-legacy": "^6.0.2", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.20.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "less": "^4.2.2", "postcss-px-to-viewport": "^1.1.1", "prettier": "^3.5.0", "terser": "^5.39.0", "typescript": "^5.7.3", "vite": "^6.1.0"}, "packageManager": "yarn@4.7.0+sha512.5a0afa1d4c1d844b3447ee3319633797bcd6385d9a44be07993ae52ff4facabccafb4af5dcd1c2f9a94ac113e5e9ff56f6130431905884414229e284e37bb7c9"}