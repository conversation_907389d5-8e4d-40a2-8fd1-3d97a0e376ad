# 聊天界面集成说明

## 概述

已成功集成腾讯云IM的chat-uikit-react组件，实现了左侧聊天界面和右侧用户信息/订单信息的布局。

## 功能特性

### 左侧聊天区域
- 会话列表 (ConversationList)
- 聊天界面 (Chat)
- 支持中文界面
- 当IM服务未配置时显示友好提示

### 右侧信息面板
- 用户信息卡片
  - 用户头像和昵称
  - 手机号
  - 主营业务
  - 个性签名
- 订单信息卡片
  - 订单列表
  - 订单状态（待支付、已支付、已退款）
  - 订单金额和时间

## 技术实现

### 依赖包
```bash
yarn add @tencentcloud/chat-uikit-react @tencentcloud/uikit-base-component-react
yarn add i18next react-i18next tslib
```

### 核心组件
- `ConversationList`: 会话列表
- `Chat`: 聊天界面
- `UIKitProvider`: UI组件提供者
- `UserInfo`: 用户信息组件

### API集成
- `postUserGetByToken()`: 获取用户信息
- `postOrderList()`: 获取订单列表
- 从URL参数获取token

## 配置说明

### 必需配置
1. **SDKAppID**: 需要在腾讯云IM控制台获取实际的SDKAppID
2. **userSig**: 用户签名，用于IM登录验证
3. **token**: 从URL参数获取，用于API认证

### 当前配置状态
- SDKAppID: 设置为示例值 `**********`（需要替换）
- 语言: 中文 (`zh-CN`)
- 主题: 浅色主题 (`light`)

## 使用方法

### 访问URL格式
```
http://localhost:5174/?token=YOUR_TOKEN_HERE
```

### 初始化流程
1. 从URL获取token参数
2. 使用token获取用户信息
3. 获取用户订单列表
4. 初始化腾讯云IM服务
5. 渲染聊天界面

### 错误处理
- 缺少token参数时显示错误提示
- 用户信息获取失败时显示错误页面
- IM初始化失败时显示聊天功能不可用提示
- 网络请求失败时显示Toast提示

## 样式特性

### 响应式布局
- 左侧聊天区域：占据70%宽度，最小60%
- 右侧信息面板：固定300px宽度
- 支持垂直滚动

### 主题色彩
- 主题色：`#30be8f`
- 成功色：`#00b42a`
- 错误色：`#f53f3f`
- 文本色：多层级灰色系统

## 下一步工作

### 必需配置
1. 在腾讯云IM控制台创建应用，获取真实的SDKAppID
2. 配置userSig生成逻辑
3. 测试IM功能

### 可选优化
1. 添加更多聊天功能（文件传输、表情包等）
2. 优化移动端适配
3. 添加消息通知功能
4. 集成更多订单操作功能

## 注意事项

1. 当前代码中的SDKAppID是示例值，需要替换为实际值
2. userSig的生成应该在服务端完成，客户端只接收
3. 确保token有效性和安全性
4. 建议在生产环境中添加更完善的错误处理和日志记录
